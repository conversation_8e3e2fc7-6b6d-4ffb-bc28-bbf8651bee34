
'use client';

import { Header } from '@/components/Header';
import { Hero } from '@/components/Hero';

export default function Home() {
  const handleDonateClick = () => {
    console.log('Donate button clicked');
    // Add your donation logic here
  };

  const handleJoinClick = () => {
    console.log('Join Now button clicked');
    // Add your join logic here
  };

  const handleLogoClick = () => {
    console.log('Logo clicked');
    // Add your logo click logic here (e.g., navigate to home)
  };

  const handleVideoLoad = () => {
    console.log('Hero video loaded successfully');
  };

  const handleVideoError = (error: Event) => {
    console.error('Hero video failed to load:', error);
  };

  return (
    <div className="min-h-screen">
      <Header
        onDonateClick={handleDonateClick}
        onJoinClick={handleJoinClick}
        onLogoClick={handleLogoClick}
      />

      {/* Mobile Menu Test Section - Remove after testing */}
      <div style={{
        padding: '20px',
        backgroundColor: '#f9f9f9',
        borderBottom: '2px solid #ddd',
        fontFamily: 'Arial, sans-serif'
      }}>
        <h3 style={{ margin: '0 0 15px 0', fontSize: '18px', color: '#333' }}>
          📱 Mobile Menu Testing Instructions:
        </h3>
        <div style={{ fontSize: '14px', lineHeight: '1.6', color: '#666' }}>
          <p><strong>1. Resize browser to mobile width (&lt;768px) or use DevTools mobile view</strong></p>
          <p><strong>2. Click hamburger menu (should be 2 lines that animate to X)</strong></p>
          <p><strong>3. Verify menu slides down from top with 1-second animation</strong></p>
          <p><strong>4. Check white background with black text</strong></p>
          <p><strong>5. Test navigation links and buttons</strong></p>
          <p><strong>6. Verify menu closes when clicking navigation items</strong></p>
        </div>
      </div>

      <Hero
        onVideoLoad={handleVideoLoad}
        onVideoError={handleVideoError}
      />
    </div>
  );
}
