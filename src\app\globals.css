@import "tailwindcss";

/* Gellix Font Face Declaration */
@font-face {
  font-family: 'Gellix';
  src: url('/fonts/gellix/Gellix-Regular.woff2') format('woff2'),
       url('/fonts/gellix/Gellix-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

:root {
  /* Color System */
  --color-primary-black: #111111;
  --color-light-gray: #E7E7E7;
  --color-accent-green: #99FF99;

  /* Additional colors for UI */
  --color-white: #ffffff;
  --color-gray: #d6d6d6;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* Typography - Font variables */
  --font-primary: var(--font-playfair, "Playfair", serif);
  --font-header: "Gellix", sans-serif;

  /* Legacy support */
  --background: var(--color-white);
  --foreground: var(--color-primary-black);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-playfair);
  --font-serif: var(--font-playfair);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--color-primary-black);
    --foreground: var(--color-light-gray);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-primary) !important;
  margin: 0;
  padding: 0;
}

/* Default font for most elements - Playfair (excluding header elements) */
h1, h2, h3, h4, h5, h6,
p, span, div, a,
input, textarea, select,
label, li, td, th {
  font-family: var(--font-primary) !important;
}

/* Specific override for header navigation and buttons to use Gellix */
.font-header,
.font-gellix,
[class*="navigation"] button,
[class*="header"] button,
[class*="Navigation"] button,
[class*="HeaderButtons"] button {
  font-family: var(--font-header) !important;
}

/* Specific font weight assignments */
h1, h2, h3 {
  font-weight: 700;
}

h4, h5, h6 {
  font-weight: 600;
}

p, span, div {
  font-weight: 400;
}

/* Font utility classes */
.font-playfair {
  font-family: var(--font-primary) !important;
}

.font-primary {
  font-family: var(--font-primary) !important;
}

.font-gellix {
  font-family: var(--font-header) !important;
}

.font-header {
  font-family: var(--font-header) !important;
}

/* Specific Navigation component font overrides */
nav[role="navigation"] button,
nav[aria-label="Main navigation"] button,
.navigation button,
.mobileNavigation button,
.navigationButton {
  font-family: var(--font-header) !important;
}

/* Header buttons font overrides */
.buttonsContainer button,
.donateButton,
.joinButton {
  font-family: var(--font-header) !important;
}

/* Override any Tailwind default font classes */
.font-sans {
  font-family: var(--font-primary) !important;
}

.font-serif {
  font-family: var(--font-primary) !important;
}
