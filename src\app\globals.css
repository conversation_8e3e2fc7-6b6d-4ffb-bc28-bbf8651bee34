@import "tailwindcss";

:root {
  /* Color System */
  --color-primary-black: #111111;
  --color-light-gray: #E7E7E7;
  --color-accent-green: #99FF99;

  /* Additional colors for UI */
  --color-white: #ffffff;
  --color-gray: #d6d6d6;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* Typography - Ensure font variable is available */
  --font-primary: var(--font-playfair-display, "Playfair Display", serif);

  /* Legacy support */
  --background: var(--color-white);
  --foreground: var(--color-primary-black);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-playfair-display);
  --font-serif: var(--font-playfair-display);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: var(--color-primary-black);
    --foreground: var(--color-light-gray);
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-playfair-display, "Playfair Display", serif) !important;
  margin: 0;
  padding: 0;
}

/* Ensure all text elements use Playfair Display with higher specificity */
* {
  font-family: var(--font-playfair-display, "Playfair Display", serif) !important;
}

h1, h2, h3, h4, h5, h6,
p, span, div, a, button,
input, textarea, select,
label, li, td, th {
  font-family: var(--font-playfair-display, "Playfair Display", serif) !important;
}

/* Specific font weight assignments */
h1, h2, h3 {
  font-weight: 700;
}

h4, h5, h6 {
  font-weight: 600;
}

p, span, div {
  font-weight: 400;
}

/* Ensure proper font loading and fallback */
.font-playfair {
  font-family: var(--font-playfair-display, "Playfair Display", serif) !important;
}

/* Additional utility classes for font application */
.font-primary {
  font-family: var(--font-playfair-display, "Playfair Display", serif) !important;
}

/* Override any Tailwind default font classes */
.font-sans {
  font-family: var(--font-playfair-display, "Playfair Display", serif) !important;
}

.font-serif {
  font-family: var(--font-playfair-display, "Playfair Display", serif) !important;
}
