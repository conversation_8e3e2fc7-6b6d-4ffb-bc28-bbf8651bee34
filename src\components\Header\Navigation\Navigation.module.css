/* Navigation Component Styles - Compact Design */

.navigation {
  display: none; /* Hidden by default on mobile */
  font-family: var(--font-header);
}

/* Desktop navigation - visible only on desktop */
@media (min-width: 768px) {
  .navigation {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-left: 1rem;
    margin-right: 1rem;
    gap: 0.5rem; /* Increased for better spacing */
  }
}

/* Mobile navigation - only visible when used in mobile menu context */
.mobileNavigation {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-family: var(--font-header);
}

/* Desktop navigation button styles */
.navigationButton {
  color: var(--color-gray-700);
  background: none;
  border: none;
  font-weight: 900; /* Updated to use Gellix SemiBold for better visibility */
  font-size: 0.85rem; /* Increased from 0.8125rem for better readability */
  letter-spacing: 0.02em;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  font-family: var(--font-header) !important;
  padding: 0.5rem 1rem; /* Increased padding for better click targets */
  white-space: nowrap;
  border-radius: 0.25rem;
  position: relative; /* Required for ::after pseudo-element positioning */
}

@media (max-width: 900px) {
  .navigationButton {
    padding: 0.5rem;
  }
}

/* Desktop hover underline animation - only for desktop navigation */
@media (min-width: 768px) {
  .navigation .navigationButton::after {
    content: '';
    position: absolute;
    bottom: -4px; /* Move below the button */
    left: 0;
    width: 100%;
    height: 3px; /* Make it thicker for testing */
    background-color: var(--color-primary-black); /* Bright green for visibility */
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease-in-out; /* Slower for testing */
  }

  .navigation .navigationButton:hover::after {
    transform: scaleX(1);
  }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  .navigation .navigationButton::after {
    transition: none;
  }
}

/* Mobile navigation button styles - only for mobile menu context */
.mobileNavigation .navigationButton {
  color: var(--color-white);
  font-size: 1.125rem; /* Slightly larger for full-screen menu */
  font-weight: 400;
  padding: 1.25rem 1.5rem; /* Increased padding for better touch targets */
  width: 100%;
  text-align: left;
  border-radius: 0.5rem;
  margin-bottom: 0.5rem;
  letter-spacing: 0.02em;
  position: relative; /* Ensure mobile buttons don't inherit desktop underline */
}

/* Ensure mobile navigation buttons don't have underline effect */
.mobileNavigation .navigationButton::after {
  display: none;
}

/* Desktop hover and focus states */
@media (min-width: 768px) {
  .navigation .navigationButton:hover {
    color: var(--color-gray-900);
    background-color: var(--color-light-gray);
  }
}

/* Mobile navigation hover and focus states */
.mobileNavigation .navigationButton:hover {
  color: var(--color-white);
  background-color: rgba(255, 255, 255, 0.1);
}


