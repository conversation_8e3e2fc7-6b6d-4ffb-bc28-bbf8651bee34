/* Header Component Styles */

.header {
  background-color: var(--color-light-gray);
  position: sticky;
  top: 0;
  z-index: 40;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
  /* box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); */
}

.container {
  width: 100%;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

.headerContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.logoContainer {
  flex-shrink: 0;
}

.navigationContainer {
  flex: 1;
  justify-content: center;
}

.buttonsContainer {
  display: none;
  flex-shrink: 0;
}

@media (min-width: 768px) {
  .buttonsContainer {
    display: flex;
  }
}

.mobileMenuButton {
  display: block;
  padding: 0.5rem;
  color: var(--color-gray-700);
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.2s ease-in-out;
}

@media (min-width: 768px) {
  .mobileMenuButton {
    display: none;
  }
}

.mobileMenuButton:hover {
  color: var(--color-gray-900);
}

.mobileMenuButton:focus {
  outline: none;
  /* Removed focus outline as requested */
}

/* Hamburger Icon Container */
.hamburgerIcon {
  width: 1.75rem;
  height: 1.125rem;
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

/* Hamburger Lines */
.hamburgerLine {
  width: 100%;
  height: 3px;
  background-color: currentColor;
  border-radius: 2px;
  transition: all 0.3s ease-in-out;
  transform-origin: center center;
  position: absolute;
}

.hamburgerLineTop {
  transform: translateY(-4.5px) rotate(0deg);
}

.hamburgerLineBottom {
  transform: translateY(4.5px) rotate(0deg);
}

/* Perfect X Animation when menu is open */
.mobileMenuButtonOpen .hamburgerLineTop {
  transform: translateY(0) rotate(45deg);
}

.mobileMenuButtonOpen .hamburgerLineBottom {
  transform: translateY(0) rotate(-45deg);
}

.mobileMenu {
  position: fixed;
  top: 4rem; /* Position directly below the header */
  left: 0;
  width: 100vw;
  height: calc(100vh - 4rem); /* Full viewport height minus header */
  background-color: var(--color-white);
  transform: translateY(-100%); /* Initially hidden off-screen to the top */
  transition: transform 1s ease-in-out; /* Updated to 1 second duration */
  z-index: 50;
  overflow-y: auto; /* Allow scrolling if content is too tall */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); /* Updated shadow for white background */
}

.mobileMenuOpen {
  transform: translateY(0); /* Slide down from top */
}

@media (min-width: 768px) {
  .mobileMenu {
    display: none;
  }
}

.mobileNavigation {
  display: flex;
  flex-direction: column;
  gap: 1.5rem; /* Reduced gap for better spacing */
  padding: 2rem 1.5rem 1.5rem 1.5rem; /* Adjusted bottom padding */
  height: 100%;
  justify-content: flex-start;
}

.mobileNavItems {
  display: flex;
  flex-direction: column;
  gap: 0.5rem; /* Tighter spacing between navigation items */
  flex: 1;
  margin-bottom: 1rem; /* Add space before buttons section */
}

.mobileButtonsContainer {
  padding-top: 1.5rem; /* Reduced padding for better proportion */
  border-top: 1px solid rgba(0, 0, 0, 0.1); /* Updated for white background */
  display: flex;
  flex-direction: column;
  gap: 0.75rem; /* Slightly tighter gap between buttons */
  margin-top: auto; /* Push buttons to bottom */
}
