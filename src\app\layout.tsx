import type { <PERSON>ada<PERSON> } from "next";
import { Playfair } from "next/font/google";
import localFont from "next/font/local";
import "./globals.css";

const playfair = Playfair({
  variable: "--font-playfair",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800", "900"],
  display: "swap",
});

const gellix = localFont({
  src: [
    {
      path: "../fonts/gellix/Gellix-Regular.woff2",
      weight: "400",
      style: "normal",
    },
  ],
  variable: "--font-gellix",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Invest Founders - Investment Platform",
  description: "Investment platform developed by Kriptaz Blockchain",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${playfair.variable} ${gellix.variable} antialiased`}
        data-new-gr-c-s-check-loaded="14.1239.0"
        data-gr-ext-installed=""
      >
        {children}
      </body>
    </html>
  );
}
